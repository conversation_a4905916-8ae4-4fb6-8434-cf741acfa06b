<template>
  <div class="flex">
    <!-- 版本 -->
    <div class="w-64">
      <BasicTitle class="mb-1" title="版本" />
      <VxeGrid
        v-bind="versionOptions"
        ref="versionRef"
        @cell-click="handleVersionCellClick"
        @checkbox-change="handleVersionCheckBoxChange"
      >
        <template #status="{ row }">
          <ElTag
            size="small"
            :type="row.status === 'ENABLED' ? 'success' : 'warning'"
          >
            {{ row.status === 'ENABLED' ? '已发布' : '已停用' }}
          </ElTag>
        </template>
      </VxeGrid>
    </div>

    <!-- 分类 -->
    <div class="ml-2 mr-2 w-96">
      <BasicTitle class="mb-1" title="分类" />
      <VxeGrid
        v-bind="categoryOptions"
        ref="categoryRef"
        @cell-click="handleCategoryCellClick"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
      </VxeGrid>
    </div>

    <!-- 明细 -->
    <div class="w-32 flex-1">
      <BasicTitle class="mb-1" title="明细" />
      <VxeGrid
        v-bind="detailOptions"
        ref="detailRef"
        @cell-click="handleDetailCellClick"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
        <template #businessCostSubjectSlot="{ row }">
          {{ filterBusinessCostAccountLabel(row.businessCostSubjectDetailIds) }}
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue';

import { ElMessage, ElTag } from 'element-plus';
import _ from 'lodash';

import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  CategoryList,
  DetailsList,
  VersionList,
} from '#/api/projectCenter/projectSetting/businessCost';
import BasicTitle from '#/components/BasicTitleBar/index.vue';
import { getNamesFromTreeByIds } from '#/utils/common';

import { materialDict } from '../../data';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
    dictType: 'MECHANICAL_DICTIONARY',
  },
);
const emit = defineEmits<{
  (e: 'componentsEvent', payload: Object): void;
}>();

const selectCategoryIds = ref([]); // 已选择的分类
const selectDetails = ref([]); // 已选择版本
const selectVersionId = ref(''); // 已选择的版本ID
const isUseVersion = ref(false); // 是否有已使用版本
const originalSelectedVersionId = ref(''); // 原始选中的版本ID，用于判断是否有变化

const currentVersion = ref({});
const versionRef = ref();
const versionOptions = reactive({
  height: 480,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkMethod: ({ row }: { row: any }) => {
      // 允许勾选的条件：
      // 1. 状态不为DISABLED，或者
      // 2. 状态为DISABLED且之前被选择过（isUse为true）且当前仍然被勾选
      return (
        row.status !== 'DISABLED' ||
        (row.status === 'DISABLED' && row.isUse && row.checked)
      );
    },
  },
  columns: [
    {
      type: 'checkbox',
      width: 40,
    },
    {
      field: 'name',
      title: '名称',
      showOverflow: 'title',
    },
    {
      field: 'orgName',
      title: '编制单位',
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
  ],
  data: [],
});
async function handleVersionCellClick({ row }) {
  currentVersion.value = row;
  // 点击版本行时，只设置当前行高亮，不自动勾选复选框
  // 复选框的勾选应该由用户手动点击复选框来控制

  // 加载对应版本的分类数据
  categoryOptions.data = await CategoryList(props.dictTypeId, row.id);
  await nextTick(() => {
    categoryRef.value.setAllTreeExpand(true);
    // 自动选择第一行分类
    if (categoryOptions.data.length > 0) {
      const firstCategory = categoryOptions.data[0];
      // 设置第一行为当前行
      categoryRef.value.setCurrentRow(firstCategory);
      // 调用分类点击处理函数，传入正确的参数格式
      handleCategoryCellClick({ row: firstCategory });
    }
  });
  // 切换版本数据置空
  selectCategoryIds.value = [];
  selectDetails.value = [];
}

function handleVersionCheckBoxChange({ row, records }: any) {
  // 获取所有当前勾选的版本
  const selectedVersions = records.filter(
    (item: any) => item.isUse || (item.status !== 'DISABLED' && !item.isUse),
  );

  // 如果当前勾选的是已禁用版本，直接处理
  if (row.status === 'DISABLED' && row.isUse) {
    // 已禁用版本被取消勾选，设置checked为false，防止再次勾选
    row.checked = false;
    selectVersionId.value = '';
    emit('componentsEvent', {
      versionId: selectVersionId.value,
      categoryIds: selectCategoryIds.value,
      details: selectDetails.value,
      hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
    });
    return;
  }

  // 检查是否已经有其他版本被勾选
  const otherSelectedVersions = selectedVersions.filter(
    (item) => item.id !== row.id,
  );

  if (otherSelectedVersions.length > 0) {
    // 如果已经有其他版本被勾选，不允许勾选新版本
    ElMessage.warning('请先取消之前选择的版本，再选择新的版本！');
    // 取消当前行的勾选
    nextTick(() => {
      versionRef.value.setCheckboxRow(row, false);
    });
    return;
  }

  // 如果勾选的是新版本（非禁用状态），更新选中状态
  if (row.status === 'DISABLED') {
    // 处理其他情况
    selectVersionId.value =
      selectedVersions.length === 1 ? selectedVersions[0].id : '';
  } else {
    // 更新选中的版本ID
    selectVersionId.value = row.id;
  }

  emit('componentsEvent', {
    versionId: selectVersionId.value,
    categoryIds: selectCategoryIds.value,
    details: selectDetails.value,
    hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
  });
}
// 分类
const categoryRef = ref();
const categoryOptions = reactive({
  height: 480,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
async function handleCategoryCellClick({ row }) {
  const dictTypeId = props.dictTypeId;
  const versionId = row.versionId;
  const categoryId = row.id;
  detailOptions.data = await DetailsList(dictTypeId, versionId, categoryId);

  // 切换分类数据置空
  selectDetails.value = [];
}

// 明细
const detailRef = ref();
const detailOptions = reactive({
  height: 480,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      field: 'specificationModel',
      title: '规格型号',
    },
    {
      width: 120,
      field: 'remark',
      title: '备注',
    },
    {
      field: 'businessCostSubjectDetailsIds',
      title: '业务成本科目名称',
      slots: {
        default: 'businessCostSubjectSlot',
      },
    },
    {
      width: 120,
      field: 'accountingDescription',
      title: '核算说明',
    },
  ],
  data: [],
});
function handleDetailCellClick() {}

// 版本校验
function versionValid(): boolean {
  // 如果没有选择版本ID，检查是否有原始版本ID（初始状态）
  if (
    selectVersionId.value.length === 0 &&
    originalSelectedVersionId.value.length === 0
  ) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  return true;
}

function validateAll(): boolean {
  return versionValid();
}

const businessCostAccountTreeList = ref([]);
// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  businessCostAccountTreeList.value =
    await TreeBusinessCostCategoryDetails(businessVersionId);
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}
watch(
  () => props.dictType,
  () => {
    getVersionList(props.dictTypeId);
    getBusinessCostAccount(currentVersion.value.businessCostSubjectVersionId);
  },
  {
    immediate: true,
  },
);

// 获取版本
async function getVersionList(dictTypeId: string) {
  versionOptions.data = await VersionList(dictTypeId);

  // 过滤出已使用的版本（包括已停用的）
  const isSelect = versionOptions.data.filter((item) => item.isUse);

  // 是否有已使用的版本
  isUseVersion.value = !_.isEmpty(isSelect);

  if (isUseVersion.value) {
    await nextTick(() => {
      // 勾选所有已使用的版本（包括已停用的）
      versionRef.value.setCheckboxRow(isSelect, true);

      // 为已禁用的版本添加checked标记
      isSelect.forEach((item) => {
        if (item.status === 'DISABLED') {
          item.checked = true;
        }
      });

      // 记录原始选中的版本ID（如果有多个，取第一个）
      originalSelectedVersionId.value = isSelect[0]?.id || '';
      selectVersionId.value = originalSelectedVersionId.value;

      // 触发初始状态事件，让父组件知道当前版本状态
      emit('componentsEvent', {
        versionId: selectVersionId.value,
        categoryIds: selectCategoryIds.value,
        details: selectDetails.value,
        hasChanged: false, // 初始状态没有变化
      });
    });
  } else {
    // 没有已使用版本时，也要触发初始事件
    emit('componentsEvent', {
      versionId: '',
      categoryIds: [],
      details: [],
      hasChanged: false,
    });
  }
}

// 获取当前选中的版本信息
function getSelectedVersionInfo() {
  return {
    versionId: selectVersionId.value,
    originalVersionId: originalSelectedVersionId.value,
    hasChanged: selectVersionId.value !== originalSelectedVersionId.value,
  };
}

defineExpose({
  validateAll,
  getSelectedVersionInfo,
});
</script>

<style scoped lang="scss"></style>
